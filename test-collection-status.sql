-- 测试存量债权清收情况数据一致性
-- 验证修复后的期末余额计算是否正确

-- 1. 使用修复后的查询（先汇总再相减）
WITH
  year_start_balance AS (
    SELECT
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 上年末余额
    FROM 减值准备表
    WHERE 年份 = 2025 - 1 AND 月份 = 12
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
    HAVING SUM(本月末债权余额) <> 0
  ),
  new_debt AS (
    SELECT
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月新增债权) AS 当年累计新增债权
    FROM 减值准备表
    WHERE 年份 = 2025 AND 月份 <= 8
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
  ),
  disposal AS (
    SELECT
      管理公司, 债权人, 债务人, 是否涉诉, 期间,
      SUM(每月处置金额) AS 当年累计处置金额
    FROM 处置表
    WHERE 年份 = 2025 AND 月份 <= 8
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
  ),
  base_data AS (
    SELECT
      k.管理公司, k.债权人, k.债务人, k.是否涉诉, k.科目名称, k.期间,
      COALESCE(y.上年末余额, 0) AS 上年末余额,
      COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
      COALESCE(d.当年累计处置金额, 0) AS 累计处置金额,
      -- 存量债权累计处置拆分逻辑
      CASE
        WHEN COALESCE(y.上年末余额, 0) > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN COALESCE(d.当年累计处置金额, 0)
        WHEN COALESCE(y.上年末余额, 0) = 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN 0
        WHEN COALESCE(y.上年末余额, 0) > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN
          CASE
            WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN 0
            ELSE COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0)
          END
        ELSE 0
      END AS 存量债权累计处置金额
    FROM (
      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
      UNION
      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
    ) k
    LEFT JOIN year_start_balance y ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
      AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
    LEFT JOIN new_debt n ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
      AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
    LEFT JOIN disposal d ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
      AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
    WHERE (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
      AND ('全部' IN ('全部', '所有公司', 'all') OR k.管理公司 = '全部')
  )
SELECT
  -- 期初金额
  COALESCE(SUM(CASE WHEN 上年末余额 > 0 THEN 上年末余额 ELSE 0 END), 0) AS 期初金额,
  -- 本年累计清收金额
  COALESCE(SUM(存量债权累计处置金额), 0) AS 本年累计清收金额,
  -- 期末余额（修复后：先汇总再相减）
  COALESCE(SUM(CASE WHEN 上年末余额 > 0 THEN 上年末余额 ELSE 0 END), 0) - 
  COALESCE(SUM(存量债权累计处置金额), 0) AS 期末余额_修复后,
  -- 期末余额（原来的：先逐行相减再汇总）
  COALESCE(SUM(CASE WHEN 上年末余额 > 0 THEN 上年末余额 - 存量债权累计处置金额 ELSE 0 END), 0) AS 期末余额_原来的,
  -- 差异
  (COALESCE(SUM(CASE WHEN 上年末余额 > 0 THEN 上年末余额 - 存量债权累计处置金额 ELSE 0 END), 0) - 
   (COALESCE(SUM(CASE WHEN 上年末余额 > 0 THEN 上年末余额 ELSE 0 END), 0) - COALESCE(SUM(存量债权累计处置金额), 0))) AS 差异金额
FROM base_data;
