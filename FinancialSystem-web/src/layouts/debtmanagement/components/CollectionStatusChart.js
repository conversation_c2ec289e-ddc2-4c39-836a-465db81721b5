import React, { useState } from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Bar } from 'react-chartjs-2';
import { Button, Box, Alert } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import CollectionStatusDetailModal from './CollectionStatusDetailModal';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 存量债权清收情况图表组件
 * @param {Object} props
 * @param {Object} props.data - 数据对象，包含 yearBeginAmount, monthDisposalAmount, yearCumulativeAmount, periodEndAmount
 * @param {String} props.title - 图表标题
 * @returns {JSX.Element}
 */
const CollectionStatusChart = ({ data, title = '存量债权清收情况' }) => {
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  // 数据验证和默认值处理
  const chartData = {
    yearBeginAmount: data?.yearBeginAmount || 0,
    monthCollectionAmount: data?.monthCollectionAmount || 0, // 更新字段名
    yearCumulativeCollectionAmount: data?.yearCumulativeCollectionAmount || 0, // 更新字段名
    periodEndAmount: data?.periodEndAmount || 0,
  };

  // 数据一致性检查
  const expectedEndAmount = chartData.yearBeginAmount - chartData.yearCumulativeCollectionAmount;
  const actualEndAmount = chartData.periodEndAmount;
  const difference = Math.abs(actualEndAmount - expectedEndAmount);
  const hasDataInconsistency = difference > 0.01; // 差异超过0.01万元认为不一致

  // 图表标签
  const labels = ['期初金额', '本月清收处置', '本年累计处置', '期末余额']; // 更新标签

  // 图表数据配置
  const barChartData = {
    labels,
    datasets: [
      {
        label: '金额',
        data: [
          chartData.yearBeginAmount,
          chartData.monthCollectionAmount,
          chartData.yearCumulativeCollectionAmount,
          chartData.periodEndAmount,
        ],
        backgroundColor: [
          'rgba(244, 67, 54, 0.8)', // 红色 - 期初金额
          'rgba(76, 175, 80, 0.8)', // 标准绿色 - 本月清收处置
          'rgba(129, 199, 132, 0.8)', // 浅绿色 - 本年累计处置
          'rgba(244, 67, 54, 0.8)', // 红色 - 期末余额
        ],
        borderColor: [
          'rgba(244, 67, 54, 1)', // 红色 - 期初金额
          'rgba(76, 175, 80, 1)', // 标准绿色 - 本月清收处置
          'rgba(129, 199, 132, 1)', // 浅绿色 - 本年累计处置
          'rgba(244, 67, 54, 1)', // 红色 - 期末余额
        ],
        borderWidth: 1,
        barThickness: 40, // 设置柱子宽度为40px（约为默认的三分之二）
      },
    ],
  };

  // 图表选项配置
  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false, // 隐藏图例，因为只有一个数据集
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            let label = context.label || '';
            if (label) {
              label += ': ';
            }
            // 显示2位小数
            label += context.parsed.y.toFixed(2) + ' 万元';
            return label;
          },
        },
      },
      datalabels: {
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: {
          size: 11,
          weight: 'bold',
        },
        formatter(value) {
          // 显示整数
          return Math.round(value).toString();
        },
      },
    },
    scales: {
      y: {
        type: 'linear',
        title: {
          display: true,
          text: '金额 (万元)',
          font: {
            size: 12,
          },
        },
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return value.toFixed(0);
          },
        },
      },
      x: {
        grid: {
          display: false, // 移除X轴网格线
        },
        ticks: {
          font: {
            size: 12,
          },
        },
      },
    },
    maintainAspectRatio: false,
  };

  // 处理更多信息按钮点击事件
  const handleMoreInfo = () => {
    setDetailModalOpen(true);
  };

  return (
    <Box
      className="p-6 bg-white rounded-lg shadow-md"
      sx={{
        position: 'relative',
        width: '100%',
        maxWidth: '100%',
        marginBottom: '20px',
        overflow: 'hidden', // 防止内容溢出
      }}
    >
      {/* 更多信息按钮 */}
      <Button
        variant="text"
        size="small"
        startIcon={<InfoOutlinedIcon />}
        onClick={handleMoreInfo}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 1,
          color: 'primary.main',
          '&:hover': {
            backgroundColor: 'rgba(76, 175, 80, 0.08)',
          },
        }}
      >
        详细信息
      </Button>

      {/* 数据一致性警告 */}
      {hasDataInconsistency && (
        <Alert
          severity="warning"
          sx={{
            mb: 2,
            fontSize: '12px',
            '& .MuiAlert-message': {
              fontSize: '12px'
            }
          }}
        >
          数据一致性提醒：期末余额与计算值存在差异 {difference.toFixed(2)} 万元
          （预期：{expectedEndAmount.toFixed(2)}，实际：{actualEndAmount.toFixed(2)}）
        </Alert>
      )}

      {/* 图表容器 */}
      <Box
        sx={{
          height: hasDataInconsistency ? '320px' : '350px', // 有警告时调整高度
          width: '100%',
          pt: 2, // 为按钮留出空间
        }}
      >
        <Bar data={barChartData} options={options} style={{ height: '100%', width: '100%' }} />
      </Box>

      {/* 数据汇总信息 */}
      <Box
        sx={{
          mt: 2,
          pt: 2,
          borderTop: '1px solid #e0e0e0',
          display: 'flex',
          justifyContent: 'space-around',
          flexWrap: 'wrap',
          gap: 1,
          width: '100%',
          overflow: 'hidden', // 防止溢出
        }}
      >
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '80px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            期初金额
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(244, 67, 54, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {chartData.yearBeginAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '90px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            本月清收处置
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(76, 175, 80, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {chartData.monthCollectionAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '90px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            本年累计处置
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(129, 199, 132, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {chartData.yearCumulativeCollectionAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '80px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            期末余额
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(244, 67, 54, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {chartData.periodEndAmount.toFixed(2)} 万元
          </Box>
        </Box>
      </Box>

      {/* 明细弹窗 */}
      <CollectionStatusDetailModal
        open={detailModalOpen}
        onClose={() => setDetailModalOpen(false)}
        data={data}
      />
    </Box>
  );
};

CollectionStatusChart.propTypes = {
  data: PropTypes.shape({
    yearBeginAmount: PropTypes.number,
    monthCollectionAmount: PropTypes.number,
    yearCumulativeCollectionAmount: PropTypes.number,
    periodEndAmount: PropTypes.number,
    yearBeginDetails: PropTypes.array,
    monthCollectionDetails: PropTypes.array,
    yearCumulativeDetails: PropTypes.array,
    periodEndDetails: PropTypes.array,
  }),
  title: PropTypes.string,
};

CollectionStatusChart.defaultProps = {
  data: {
    yearBeginAmount: 0,
    monthCollectionAmount: 0,
    yearCumulativeCollectionAmount: 0,
    periodEndAmount: 0,
    yearBeginDetails: [],
    monthCollectionDetails: [],
    yearCumulativeDetails: [],
    periodEndDetails: [],
  },
  title: '存量债权清收情况',
};

export default CollectionStatusChart;
